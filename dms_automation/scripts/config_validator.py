#!/usr/bin/env python3
"""
配置文件验证器 - 智能检查和交互确认缺失的配置文件

功能：
1. 自动检测C++工具包中缺失的配置文件
2. 提供交互式配置文件创建
3. 验证远端服务连接
4. 对比模型文件一致性
"""

import os
import json
import socket
import hashlib
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Prompt, Confirm
from rich.table import Table

console = Console()

class ConfigValidator:
    """配置文件验证器"""
    
    def __init__(self, cpp_kit_path: str):
        self.cpp_kit_path = Path(cpp_kit_path)
        self.required_files = {
            "ip_port.json": "远端服务配置",
            "calidata.json": "相机校准数据"
        }
        self.optional_files = {
            "config.json": "其他配置参数"
        }
        
    def check_missing_files(self) -> Dict[str, bool]:
        """检查缺失的配置文件"""
        missing_files = {}
        
        for file_name, description in self.required_files.items():
            file_path = self.cpp_kit_path / file_name
            missing_files[file_name] = not file_path.exists()
            
        return missing_files
    
    def create_ip_port_template(self) -> Dict:
        """创建ip_port.json模板"""
        return {
            "ip": "*************",
            "port": 8080,
            "timeout": 30,
            "description": "DMS远端服务配置"
        }
    
    def create_calidata_template(self) -> Dict:
        """创建calidata.json模板"""
        return {
            "camera_matrix": [
                [800.0, 0.0, 320.0],
                [0.0, 800.0, 240.0],
                [0.0, 0.0, 1.0]
            ],
            "distortion_coeffs": [0.1, -0.2, 0.0, 0.0, 0.0],
            "image_size": [640, 480],
            "description": "相机内参和畸变系数"
        }
    
    def interactive_config_creation(self) -> bool:
        """交互式配置文件创建"""
        console.print(Panel(
            "🔧 配置文件检查与创建\n"
            "检测到缺失的配置文件，将引导您创建必要的配置。",
            title="配置验证器",
            border_style="blue"
        ))
        
        missing_files = self.check_missing_files()
        
        if not any(missing_files.values()):
            console.print("✅ 所有必需的配置文件都存在")
            return True
        
        # 显示缺失文件表格
        table = Table(title="缺失的配置文件")
        table.add_column("文件名", style="cyan")
        table.add_column("描述", style="magenta")
        table.add_column("状态", style="red")
        
        for file_name, is_missing in missing_files.items():
            if is_missing:
                description = self.required_files.get(file_name, "配置文件")
                table.add_row(file_name, description, "❌ 缺失")
        
        console.print(table)
        
        # 询问是否创建配置文件
        if not Confirm.ask("\n是否要创建缺失的配置文件？"):
            console.print("❌ 用户取消配置创建")
            return False
        
        # 创建每个缺失的配置文件
        for file_name, is_missing in missing_files.items():
            if is_missing:
                if not self._create_config_file(file_name):
                    return False
        
        console.print("✅ 所有配置文件创建完成")
        return True
    
    def _create_config_file(self, file_name: str) -> bool:
        """创建单个配置文件"""
        console.print(f"\n📝 创建配置文件: {file_name}")
        
        if file_name == "ip_port.json":
            return self._create_ip_port_config()
        elif file_name == "calidata.json":
            return self._create_calidata_config()
        else:
            console.print(f"❌ 未知的配置文件类型: {file_name}")
            return False
    
    def _create_ip_port_config(self) -> bool:
        """创建ip_port.json配置"""
        template = self.create_ip_port_template()
        
        console.print("请输入远端DMS服务配置:")
        
        # 获取用户输入
        ip = Prompt.ask("IP地址", default=template["ip"])
        port = Prompt.ask("端口", default=str(template["port"]))
        
        try:
            port = int(port)
        except ValueError:
            console.print("❌ 端口必须是数字")
            return False
        
        # 创建配置
        config = {
            "ip": ip,
            "port": port,
            "timeout": template["timeout"],
            "description": template["description"]
        }
        
        # 保存文件
        config_path = self.cpp_kit_path / "ip_port.json"
        try:
            with open(config_path, 'w') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            console.print(f"✅ 已创建: {config_path}")
            return True
        except Exception as e:
            console.print(f"❌ 创建失败: {e}")
            return False
    
    def _create_calidata_config(self) -> bool:
        """创建calidata.json配置"""
        template = self.create_calidata_template()
        
        console.print("相机校准数据配置:")
        console.print("1. 使用默认模板（适用于测试）")
        console.print("2. 手动输入参数")
        
        choice = Prompt.ask("请选择", choices=["1", "2"], default="1")
        
        if choice == "1":
            config = template
        else:
            # 简化的手动输入（实际使用中可以更详细）
            console.print("请输入相机参数（简化版）:")
            fx = float(Prompt.ask("焦距fx", default="800.0"))
            fy = float(Prompt.ask("焦距fy", default="800.0"))
            cx = float(Prompt.ask("主点cx", default="320.0"))
            cy = float(Prompt.ask("主点cy", default="240.0"))
            
            config = template.copy()
            config["camera_matrix"] = [
                [fx, 0.0, cx],
                [0.0, fy, cy],
                [0.0, 0.0, 1.0]
            ]
        
        # 保存文件
        config_path = self.cpp_kit_path / "calidata.json"
        try:
            with open(config_path, 'w') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            console.print(f"✅ 已创建: {config_path}")
            return True
        except Exception as e:
            console.print(f"❌ 创建失败: {e}")
            return False
    
    def verify_remote_service(self) -> bool:
        """验证远端服务连接"""
        ip_port_file = self.cpp_kit_path / "ip_port.json"
        
        if not ip_port_file.exists():
            console.print("❌ ip_port.json文件不存在")
            return False
        
        try:
            with open(ip_port_file, 'r') as f:
                config = json.load(f)
            
            ip = config.get("ip")
            port = config.get("port")
            timeout = config.get("timeout", 10)
            
            console.print(f"🔍 验证远端服务: {ip}:{port}")
            
            # 测试连接
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            
            result = sock.connect_ex((ip, port))
            sock.close()
            
            if result == 0:
                console.print("✅ 远端服务连接成功")
                return True
            else:
                console.print(f"❌ 远端服务连接失败 (错误码: {result})")
                return False
                
        except Exception as e:
            console.print(f"❌ 验证远端服务时出错: {e}")
            return False
    
    def verify_model_files(self) -> bool:
        """验证模型文件完整性"""
        model_dir = self.cpp_kit_path / "model"
        
        if not model_dir.exists():
            console.print("❌ model目录不存在")
            return False
        
        expected_models = ["eye.ovm", "FaceDetection.ovm", "FaceKeypoints.ovm"]
        
        console.print("🔍 验证模型文件:")
        
        for model_file in expected_models:
            model_path = model_dir / model_file
            if model_path.exists():
                # 计算MD5
                md5_hash = self._calculate_md5(model_path)
                file_size = model_path.stat().st_size
                console.print(f"  ✅ {model_file}: {file_size} bytes, MD5: {md5_hash[:8]}...")
            else:
                console.print(f"  ❌ {model_file}: 文件缺失")
                return False
        
        return True
    
    def _calculate_md5(self, file_path: Path) -> str:
        """计算文件MD5"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def full_validation(self) -> bool:
        """完整验证流程"""
        console.print(Panel(
            "开始C++工具包配置验证流程",
            title="配置验证器",
            border_style="green"
        ))
        
        # 1. 检查和创建配置文件
        if not self.interactive_config_creation():
            return False
        
        # 2. 验证模型文件
        if not self.verify_model_files():
            console.print("❌ 模型文件验证失败")
            return False
        
        # 3. 验证远端服务（可选）
        if Confirm.ask("是否验证远端服务连接？"):
            if not self.verify_remote_service():
                console.print("⚠️  远端服务验证失败，但可以继续本地处理")
        
        console.print(Panel(
            "✅ 配置验证完成，可以开始处理流程",
            title="验证成功",
            border_style="green"
        ))
        
        return True


def main():
    """主函数 - 用于独立测试"""
    import sys
    
    if len(sys.argv) != 2:
        print("用法: python config_validator.py <cpp_kit_path>")
        sys.exit(1)
    
    cpp_kit_path = sys.argv[1]
    validator = ConfigValidator(cpp_kit_path)
    
    if validator.full_validation():
        print("✅ 验证成功")
        sys.exit(0)
    else:
        print("❌ 验证失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
