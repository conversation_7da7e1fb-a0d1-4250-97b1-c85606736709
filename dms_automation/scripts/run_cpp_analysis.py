#!/usr/bin/env python3
"""
DMS自动化分析与渲染平台 - 阶段二：C++批量处理脚本
基于dms_postmortem_optimised.py改造，实现帧资产包的批量C++分析处理
"""

import os
import sys
import json
import time
import subprocess
import threading
import hashlib
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加脚本目录到Python路径
script_dir = Path(__file__).parent
sys.path.insert(0, str(script_dir))

# 导入核心模块
from core_modules import (
    Config, ResourceMonitor, ProgressDisplay, console
)
from asset_package_manager import AssetPackageManager
from config_validator import ConfigValidator

class CppKitManager:
    """C++工具包管理器"""
    
    def __init__(self, cpp_kits_dir: str = "cpp_kits"):
        self.cpp_kits_dir = Path(cpp_kits_dir)
        self.available_kits = {}
        self._load_available_kits()
    
    def _load_available_kits(self):
        """加载可用的C++工具包"""
        if not self.cpp_kits_dir.exists():
            return
        
        for kit_dir in self.cpp_kits_dir.iterdir():
            if kit_dir.is_dir():
                metadata_file = kit_dir / "metadata.json"
                if metadata_file.exists():
                    try:
                        with open(metadata_file, 'r') as f:
                            metadata = json.load(f)
                        
                        kit_name = metadata['kit_info']['name']
                        executable_path = kit_dir / metadata['main_executable']
                        self.available_kits[kit_name] = {
                            'path': str(kit_dir),
                            'metadata': metadata,
                            'executable': str(executable_path)
                        }
                    except Exception as e:
                        if console:
                            console.print(f"[yellow]警告: 无法加载工具包 {kit_dir.name}: {e}[/yellow]")
    
    def get_kit(self, kit_name: str) -> Optional[Dict[str, Any]]:
        """获取指定的C++工具包"""
        return self.available_kits.get(kit_name)
    
    def list_kits(self) -> List[str]:
        """列出所有可用的C++工具包"""
        return list(self.available_kits.keys())

class CppAnalysisProcessor:
    """C++分析处理器"""
    
    def __init__(self, config: Config, cpp_kit_manager: CppKitManager, asset_manager: AssetPackageManager = None):
        self.config = config
        self.cpp_kit_manager = cpp_kit_manager
        self.asset_manager = asset_manager or AssetPackageManager()
        self.resource_monitor = ResourceMonitor()
        self.results_dir = Path("results")
        self.results_dir.mkdir(exist_ok=True)
        
        # 处理统计
        self.processed_count = 0
        self.failed_count = 0
        self.total_processing_time = 0.0
    
    def call_cpp_program(self, kit_info: Dict[str, Any], input_file: str, 
                        output_dir: str, timeout: int = 300) -> Tuple[bool, str]:
        """调用C++程序进行分析
        
        Args:
            kit_info: C++工具包信息
            input_file: 输入文件路径
            output_dir: 输出目录
            timeout: 超时时间（秒）
            
        Returns:
            (是否成功, 结果信息)
        """
        try:
            executable_path = kit_info['executable']
            kit_path = kit_info['path']

            if not os.path.exists(executable_path):
                return False, f"可执行文件不存在: {executable_path}"
            
            # 设置环境变量
            env = os.environ.copy()
            env['LD_LIBRARY_PATH'] = f"{kit_path}:{env.get('LD_LIBRARY_PATH', '')}"
            
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)
            
            # 构建命令
            cmd = [executable_path, input_file]
            
            if console:
                console.print(f"[blue]执行C++程序: {' '.join(cmd)}[/blue]")
            
            # 启动进程
            start_time = time.time()
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                env=env,
                cwd=kit_path
            )
            
            # 等待完成或超时
            try:
                stdout, stderr = process.communicate(timeout=timeout)
                processing_time = time.time() - start_time
                
                if process.returncode == 0:
                    # 保存输出日志
                    log_file = Path(output_dir) / "cpp_output.log"
                    with open(log_file, 'w') as f:
                        f.write(f"=== C++程序执行日志 ===\n")
                        f.write(f"命令: {' '.join(cmd)}\n")
                        f.write(f"执行时间: {processing_time:.2f}秒\n")
                        f.write(f"返回码: {process.returncode}\n\n")
                        f.write("=== 标准输出 ===\n")
                        f.write(stdout)
                        f.write("\n=== 标准错误 ===\n")
                        f.write(stderr)
                    
                    return True, f"处理成功，耗时 {processing_time:.2f}秒"
                else:
                    return False, f"C++程序返回错误码 {process.returncode}: {stderr}"
                    
            except subprocess.TimeoutExpired:
                process.kill()
                return False, f"C++程序执行超时（{timeout}秒）"
                
        except Exception as e:
            return False, f"C++程序执行异常: {e}"
    
    def create_input_file(self, asset_metadata: Dict[str, Any],
                         frame_files: List[Path], output_dir: str) -> str:
        """创建C++程序的输入文件

        Args:
            asset_metadata: 资产包元数据
            frame_files: 帧文件列表
            output_dir: 输出目录

        Returns:
            输入文件路径
        """
        input_file = Path(output_dir) / "input_frames.txt"

        # C++程序期望的格式是: video_path,[start:end]
        # 对于帧序列，我们使用第一个帧的路径作为"视频路径"，帧范围为[:]表示全部
        if frame_files:
            # 使用第一个帧文件的目录作为"视频路径"
            frames_dir = frame_files[0].parent
            with open(input_file, 'w') as f:
                f.write(f"{frames_dir},[:]")
        else:
            # 如果没有帧文件，创建空文件
            with open(input_file, 'w') as f:
                f.write("")

        return str(input_file)
    
    def process_asset_package(self, asset_name: str, kit_name: str) -> Tuple[bool, str]:
        """处理单个帧资产包
        
        Args:
            asset_name: 资产包名称
            kit_name: C++工具包名称
            
        Returns:
            (是否成功, 结果信息)
        """
        try:
            # 获取C++工具包
            kit_info = self.cpp_kit_manager.get_kit(kit_name)
            if not kit_info:
                return False, f"C++工具包不存在: {kit_name}"

            # 配置验证 - 智能检查和交互确认缺失文件
            console.print(f"\n🔧 验证C++工具包配置: {kit_name}")
            validator = ConfigValidator(kit_info['path'])
            if not validator.full_validation():
                return False, "配置验证失败，请检查配置文件"
            
            # 加载资产包
            try:
                metadata, frame_files = self.asset_manager.load_asset_package(asset_name)
            except Exception as e:
                return False, f"加载资产包失败: {e}"
            
            # 创建结果目录
            result_dir = self.results_dir / f"{asset_name}_{kit_name}_{int(time.time())}"
            result_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建输入文件
            input_file = self.create_input_file(metadata, frame_files, str(result_dir))
            
            # 调用C++程序
            success, message = self.call_cpp_program(
                kit_info, input_file, str(result_dir),
                timeout=self.config.get('processing_timeout', 300)
            )
            
            if success:
                # 保存处理结果元数据
                result_metadata = {
                    "asset_info": metadata.get("asset_info", {}),
                    "kit_info": kit_info['metadata']['kit_info'],
                    "processing_info": {
                        "processed_at": time.strftime("%Y-%m-%d %H:%M:%S"),
                        "input_frames": len(frame_files),
                        "result_dir": str(result_dir),
                        "status": "success",
                        "message": message
                    }
                }
                
                result_metadata_file = result_dir / "result_metadata.json"
                with open(result_metadata_file, 'w') as f:
                    json.dump(result_metadata, f, indent=2, ensure_ascii=False)
                
                self.processed_count += 1
                return True, f"处理成功: {result_dir}"
            else:
                self.failed_count += 1
                return False, message
                
        except Exception as e:
            self.failed_count += 1
            return False, f"处理异常: {e}"
    
    def batch_process(self, asset_names: List[str], kit_names: List[str], 
                     max_workers: int = None) -> Dict[str, Any]:
        """批量处理多个资产包
        
        Args:
            asset_names: 资产包名称列表
            kit_names: C++工具包名称列表
            max_workers: 最大并发数
            
        Returns:
            处理结果统计
        """
        if max_workers is None:
            max_workers = self.resource_monitor.get_optimal_thread_count()
        
        # 生成所有处理任务
        tasks = []
        for asset_name in asset_names:
            for kit_name in kit_names:
                tasks.append((asset_name, kit_name))
        
        total_tasks = len(tasks)
        if console:
            console.print(f"[blue]开始批量处理: {len(asset_names)}个资产包 × {len(kit_names)}个工具包 = {total_tasks}个任务[/blue]")
        
        # 初始化进度显示
        progress_display = ProgressDisplay(total_tasks)
        progress_display.start()
        
        results = []
        start_time = time.time()
        
        try:
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有任务
                future_to_task = {
                    executor.submit(self.process_asset_package, asset_name, kit_name): (asset_name, kit_name)
                    for asset_name, kit_name in tasks
                }
                
                # 处理完成的任务
                for i, future in enumerate(as_completed(future_to_task)):
                    asset_name, kit_name = future_to_task[future]
                    
                    try:
                        success, message = future.result()
                        results.append({
                            'asset_name': asset_name,
                            'kit_name': kit_name,
                            'success': success,
                            'message': message
                        })
                        
                        # 更新进度
                        status = 'completed' if success else 'failed'
                        progress_percent = ((i + 1) / total_tasks) * 100
                        progress_message = f"{asset_name} + {kit_name}: {'成功' if success else '失败'}"
                        progress_display.update_segment(i, status, progress_percent, progress_message)
                        
                    except Exception as e:
                        results.append({
                            'asset_name': asset_name,
                            'kit_name': kit_name,
                            'success': False,
                            'message': f"任务异常: {e}"
                        })
                        progress_display.update_segment(i, 'failed', ((i + 1) / total_tasks) * 100, f"{asset_name} + {kit_name}: 异常")
        
        finally:
            progress_display.stop()
        
        # 统计结果
        total_time = time.time() - start_time
        successful_tasks = sum(1 for r in results if r['success'])
        failed_tasks = len(results) - successful_tasks
        
        summary = {
            'total_tasks': total_tasks,
            'successful_tasks': successful_tasks,
            'failed_tasks': failed_tasks,
            'total_time': total_time,
            'average_time_per_task': total_time / total_tasks if total_tasks > 0 else 0,
            'results': results
        }
        
        return summary

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(
        description='DMS自动化分析与渲染平台 - 阶段二：C++批量处理',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python3 run_cpp_analysis.py --assets test_asset_001 --kits v1.0
  python3 run_cpp_analysis.py --assets test_asset_001,test_asset_002 --kits v1.0,v2.0 --max-workers 4
  python3 run_cpp_analysis.py --list-assets
  python3 run_cpp_analysis.py --list-kits
        """
    )
    
    parser.add_argument('--assets', help='资产包名称，多个用逗号分隔')
    parser.add_argument('--kits', help='C++工具包名称，多个用逗号分隔')
    parser.add_argument('--max-workers', type=int, help='最大并发数')
    parser.add_argument('--config', help='配置文件路径')
    parser.add_argument('--list-assets', action='store_true', help='列出所有可用的资产包')
    parser.add_argument('--list-kits', action='store_true', help='列出所有可用的C++工具包')
    parser.add_argument('--timeout', type=int, default=300, help='单个任务超时时间（秒）')
    
    args = parser.parse_args()
    
    try:
        # 加载配置
        config = Config(args.config)
        config.config['processing_timeout'] = args.timeout
        
        # 初始化管理器
        cpp_kit_manager = CppKitManager()
        asset_manager = AssetPackageManager()
        
        # 列出资产包
        if args.list_assets:
            assets = asset_manager.list_asset_packages()
            if console:
                console.print(f"[blue]可用的资产包 ({len(assets)}个):[/blue]")
                for asset in assets:
                    console.print(f"  - {asset}")
            else:
                print(f"可用的资产包 ({len(assets)}个):")
                for asset in assets:
                    print(f"  - {asset}")
            return 0
        
        # 列出C++工具包
        if args.list_kits:
            kits = cpp_kit_manager.list_kits()
            if console:
                console.print(f"[blue]可用的C++工具包 ({len(kits)}个):[/blue]")
                for kit in kits:
                    console.print(f"  - {kit}")
            else:
                print(f"可用的C++工具包 ({len(kits)}个):")
                for kit in kits:
                    print(f"  - {kit}")
            return 0
        
        # 检查必需参数
        if not args.assets or not args.kits:
            parser.print_help()
            return 1
        
        # 解析参数
        asset_names = [name.strip() for name in args.assets.split(',')]
        kit_names = [name.strip() for name in args.kits.split(',')]
        
        # 验证资产包和工具包存在
        available_assets = asset_manager.list_asset_packages()
        available_kits = cpp_kit_manager.list_kits()
        
        for asset_name in asset_names:
            if asset_name not in available_assets:
                if console:
                    console.print(f"[red]错误: 资产包不存在: {asset_name}[/red]")
                else:
                    print(f"错误: 资产包不存在: {asset_name}")
                return 1
        
        for kit_name in kit_names:
            if kit_name not in available_kits:
                if console:
                    console.print(f"[red]错误: C++工具包不存在: {kit_name}[/red]")
                else:
                    print(f"错误: C++工具包不存在: {kit_name}")
                return 1
        
        # 初始化处理器
        processor = CppAnalysisProcessor(config, cpp_kit_manager)
        
        # 开始批量处理
        if console:
            console.print(f"[green]开始批量处理...[/green]")
        else:
            print("开始批量处理...")
        
        summary = processor.batch_process(asset_names, kit_names, args.max_workers)
        
        # 显示结果
        if console:
            console.print(f"\n[green]批量处理完成！[/green]")
            console.print(f"[blue]总任务数: {summary['total_tasks']}[/blue]")
            console.print(f"[green]成功: {summary['successful_tasks']}[/green]")
            console.print(f"[red]失败: {summary['failed_tasks']}[/red]")
            console.print(f"[blue]总耗时: {summary['total_time']:.2f}秒[/blue]")
            console.print(f"[blue]平均耗时: {summary['average_time_per_task']:.2f}秒/任务[/blue]")
        else:
            print(f"\n批量处理完成！")
            print(f"总任务数: {summary['total_tasks']}")
            print(f"成功: {summary['successful_tasks']}")
            print(f"失败: {summary['failed_tasks']}")
            print(f"总耗时: {summary['total_time']:.2f}秒")
            print(f"平均耗时: {summary['average_time_per_task']:.2f}秒/任务")
        
        # 保存处理报告
        report_file = f"results/batch_processing_report_{int(time.time())}.json"
        with open(report_file, 'w') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        if console:
            console.print(f"[blue]处理报告已保存: {report_file}[/blue]")
        else:
            print(f"处理报告已保存: {report_file}")
        
        return 0 if summary['failed_tasks'] == 0 else 1
        
    except KeyboardInterrupt:
        if console:
            console.print("\n[yellow]用户中断处理[/yellow]")
        else:
            print("\n用户中断处理")
        return 130
    except Exception as e:
        if console:
            console.print(f"\n[red]程序异常: {e}[/red]")
        else:
            print(f"\n程序异常: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
